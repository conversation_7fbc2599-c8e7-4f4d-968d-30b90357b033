# 📚 Binance-Lighter套利交易系统 - 文档中心

## 📋 文档索引

### 🚀 快速开始
- [主项目文档](../README.md) - 项目概述和快速开始指南
- [模拟交易指南](PAPER_TRADING_GUIDE.md) - 安全的模拟交易模式使用指南
- [安全配置指南](SECURITY_GUIDE.md) - API密钥和安全配置 **（必读）**

### 🔧 系统管理
- [进程管理指南](PROCESS_MANAGEMENT.md) - 系统进程管理和监控
- [系统状态文档](SYSTEM_STATUS.md) - 当前系统状态和性能指标

### 📚 技术文档 **（新增）**
- [API参考文档](API_REFERENCE.md) - 完整的REST API和WebSocket文档
- [更新日志](CHANGELOG.md) - 版本更新记录和新功能介绍

### 🧪 测试和工具
- [测试脚本说明](../tests/README.md) - 各种测试和验证脚本
- [工具脚本说明](../scripts/README.md) - 部署和维护工具

## 🎯 主要功能文档

### 核心功能
- **实时套利交易**: 自动监控价差并执行套利交易
- **风险管理**: 多层次风险控制和紧急停止机制
- **Web监控界面**: 实时监控系统状态和交易表现
- **数据持久化**: 完整的交易记录和历史数据管理

### 技术特性
- **异步架构**: 基于Python asyncio的高性能设计
- **模块化设计**: 清晰的模块分离，易于维护和扩展
- **实时数据流**: WebSocket连接和实时价格数据
- **单实例保护**: 防止多实例同时运行的安全机制

## 📊 系统架构

```
套利交易系统
├── 数据获取层 (Binance + Lighter WebSocket)
├── 策略分析层 (价差分析 + 技术指标)
├── 交易执行层 (订单管理 + 风险控制)
├── 数据存储层 (SQLite + 实时缓存)
└── 监控界面层 (Web UI + RESTful API)
```

## 🔒 安全和风险控制

### 安全特性
- **API密钥保护**: 配置文件加密和环境变量管理
- **模拟交易模式**: 零风险的纸上交易测试
- **单实例保护**: 防止意外的多实例运行
- **完整日志**: 所有操作的详细审计日志

### 风险管理
- **仓位控制**: 最大持仓量和单次交易量限制
- **止损机制**: 日亏损限制和最大回撤控制
- **实时监控**: 风险指标实时计算和告警
- **紧急停止**: 一键停止所有交易活动

## 🚀 快速开始

1. **安装依赖**: `pip install -r requirements.txt`
2. **配置API**: 复制并编辑 `config/exchanges.yaml`
3. **启动系统**: `python run.py --paper-trading`
4. **访问监控**: http://localhost:8001

## 📞 获取帮助

如果您在使用过程中遇到问题：

1. 查看相关文档
2. 检查系统状态: `python run.py --status`
3. 查看日志文件: `logs/arbitrage.log`
4. 运行系统诊断: `python run.py --test-system`

## 📈 性能指标

- **代码行数**: 7,300+ 行
- **模块数量**: 15+ 个核心模块
- **API响应时间**: < 10ms
- **数据更新频率**: 每2秒
- **连接稳定性**: 99.9%+

---

**最后更新**: 2025-06-01
**文档版本**: v2.3.0
**系统状态**: ✅ 生产就绪

## 🆕 最新更新 (v1.1.0 - 2025-06-01)

### 新增文档
- **[API参考文档](API_REFERENCE.md)**: 完整的REST API和WebSocket接口文档
- **[更新日志](CHANGELOG.md)**: 详细的版本更新记录和功能介绍

### 界面优化文档更新
- 统一面板高度设计说明
- 价差MA计算公式显示功能
- 分离交易记录面板功能
- 新增挂单管理API文档

### 🆕 新用户指南
1. **首先阅读** [安全配置指南](SECURITY_GUIDE.md) - 确保安全配置 🔒
2. 然后阅读 [模拟交易指南](PAPER_TRADING_GUIDE.md) - 了解模拟交易模式
3. 了解 [进程管理指南](PROCESS_MANAGEMENT.md) - 学习系统管理
4. 查看 [系统状态文档](SYSTEM_STATUS.md) - 了解系统状态

### 👨‍💻 开发者指南
1. **首先阅读** [安全配置指南](SECURITY_GUIDE.md) - 确保开发环境安全
2. 查看 [主项目文档](../README.md) - 了解技术架构
3. 阅读 [系统状态文档](SYSTEM_STATUS.md) - 了解系统状态
4. 参考 [测试脚本说明](../tests/README.md) - 了解测试方法

### 🔧 维护者指南
1. 查看 [安全配置指南](SECURITY_GUIDE.md) - 了解安全要求
2. 了解 [进程管理指南](PROCESS_MANAGEMENT.md) - 掌握进程管理
3. 参考 [工具脚本说明](../scripts/README.md) - 了解维护工具
4. 定期更新文档索引和链接

### 🚨 故障排除指南
1. 检查 [安全配置指南](SECURITY_GUIDE.md) - 确认配置安全
2. 查看 [进程管理指南](PROCESS_MANAGEMENT.md) - 故障排除步骤
3. 参考 [模拟交易指南](PAPER_TRADING_GUIDE.md) - 常见问题解答
4. 查看 [系统状态文档](SYSTEM_STATUS.md) - 了解系统状态

## 📖 文档更新记录

- **2025年5月**: 文档整理和更新，移除过时引用，优化导航结构
- **2025年5月**: 项目整理完成，删除过时文件，优化项目结构
- **2025年5月**: 实时更新功能修复完成，MCP验证通过
- **2025年**: 完成项目开发，创建完整文档体系

## 🔒 安全提醒

⚠️ **重要**: 在使用系统之前，请务必阅读 [安全配置指南](SECURITY_GUIDE.md)，确保：
- API密钥安全存储
- 配置文件正确设置
- 环境变量妥善管理
- 权限最小化配置

## 💡 文档贡献

如果您发现文档中的错误或需要补充，欢迎：
1. 提交Issue报告问题
2. 提交Pull Request改进文档
3. 通过邮件联系维护者

## 🔍 快速搜索

- **安全配置相关**: 查看 [SECURITY_GUIDE.md](SECURITY_GUIDE.md) 🔒
- **模拟交易相关**: 查看 [PAPER_TRADING_GUIDE.md](PAPER_TRADING_GUIDE.md)
- **进程管理相关**: 查看 [PROCESS_MANAGEMENT.md](PROCESS_MANAGEMENT.md)
- **系统状态相关**: 查看 [SYSTEM_STATUS.md](SYSTEM_STATUS.md)
- **API接口相关**: 查看 [API_REFERENCE.md](API_REFERENCE.md) **（新增）**
- **版本更新相关**: 查看 [CHANGELOG.md](CHANGELOG.md) **（新增）**
- **测试脚本相关**: 查看 [tests/README.md](../tests/README.md)
- **工具脚本相关**: 查看 [scripts/README.md](../scripts/README.md)

---

**返回项目主页**: [../README.md](../README.md)